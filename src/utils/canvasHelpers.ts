import type { ShowcaseConfig, TextElement } from '../types/showcase.types';

export const generateId = (): string => {
  return Math.random().toString(36).substring(2, 11);
};

export const createDefaultTextElement = (content: string, x: number, y: number): TextElement => ({
  id: generateId(),
  content,
  x,
  y,
  fontSize: 64,
  fontFamily: 'Inter',
  color: '#FFFFFF',
  rotation: 0,
  shadow: {
    enabled: false,
    offsetX: 2,
    offsetY: 2,
    blur: 4,
    color: '#000000',
    opacity: 0.5,
  },
});

export const defaultShowcaseConfig: ShowcaseConfig = {
  id: '',
  screenshotUrl: '',
  title: 'Your App Name',
  subtitle: 'Amazing app description',
  background: {
    type: 'gradient',
    value: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    gradient: {
      type: 'linear',
      angle: 135,
      colors: [
        { color: '#667eea', position: 0 },
        { color: '#764ba2', position: 100 }
      ]
    }
  },
  text: {
    fontSize: 64,
    fontFamily: 'Inter',
    color: '#FFFFFF',
    titleX: 540,
    titleY: 200,
    subtitleX: 540,
    subtitleY: 300,
  },
  textElements: [
    createDefaultTextElement('Your App Name', 540, 200),
    createDefaultTextElement('Amazing app description', 540, 300),
  ],
  screenshot: {
    x: 540,
    y: 1100,
    width: 800,
    height: 1422,
    borderRadius: 40,
    rotation: 0,
    shadow: {
      enabled: false,
      offsetX: 4,
      offsetY: 4,
      blur: 8,
      color: '#000000',
      opacity: 0.3,
    },
  },
};



export const createDefaultShowcase = (screenshotUrl: string): ShowcaseConfig => {
  return {
    ...defaultShowcaseConfig,
    id: generateId(),
    screenshotUrl,
  };
};

export const exportCanvasToImage = async (
  canvas: HTMLCanvasElement,
  format: 'png' | 'jpg' = 'png',
  quality = 1
): Promise<string> => {
  return new Promise((resolve) => {
    const dataURL = canvas.toDataURL(`image/${format}`, quality);
    resolve(dataURL);
  });
};

export const downloadImage = (dataURL: string, filename: string) => {
  const link = document.createElement('a');
  link.download = filename;
  link.href = dataURL;
  link.style.display = 'none';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

export const createGradientBackground = (colors: string[]): string => {
  if (colors.length === 1) return colors[0];
  return `linear-gradient(135deg, ${colors.join(', ')})`;
};

export const generateGradientCSS = (gradient: {
  type: 'linear' | 'radial';
  angle: number;
  colors: Array<{ color: string; position: number }>;
}): string => {
  const colorStops = gradient.colors
    .sort((a, b) => a.position - b.position)
    .map(stop => `${stop.color} ${stop.position}%`)
    .join(', ');

  if (gradient.type === 'radial') {
    return `radial-gradient(circle, ${colorStops})`;
  } else {
    return `linear-gradient(${gradient.angle}deg, ${colorStops})`;
  }
};