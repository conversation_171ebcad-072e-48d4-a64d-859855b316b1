import React from 'react';
import { Upload, Download, Sparkles } from 'lucide-react';

interface TopNavigationProps {
  onImport: () => void;
  onExport: () => void;
  importDisabled?: boolean;
  exportDisabled?: boolean;
}

export const TopNavigation: React.FC<TopNavigationProps> = ({
  onImport,
  onExport,
  importDisabled = false,
  exportDisabled = false,
}) => {
  return (
    <header className="bg-white border-b border-gray-200 px-6 py-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <Sparkles className="h-8 w-8 text-primary-600" />
          <div>
            <h1 className="text-xl font-bold text-gray-900">Screenshot2Show</h1>
            <p className="text-sm text-gray-600">App Store Showcase Generator</p>
          </div>
        </div>
        
        <div className="flex items-center space-x-3">
          <button
            onClick={onImport}
            disabled={importDisabled}
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            <Upload className="h-4 w-4 mr-2" />
            Import Screenshots
          </button>

          <button
            onClick={onExport}
            disabled={exportDisabled}
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            <Download className="h-4 w-4 mr-2" />
            Export All
          </button>
        </div>
      </div>
    </header>
  );
};