import React, { useState } from 'react';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  type DragEndEvent,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  horizontalListSortingStrategy,
} from '@dnd-kit/sortable';
import {
  useSortable,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { GripVertical, Plus, Trash2, X } from 'lucide-react';
import { ShowcaseCanvas } from './ShowcaseCanvas';
import { InteractiveCanvas } from './InteractiveCanvas';
import type { ShowcaseItem } from '../types/showcase.types';

interface SortableShowcaseItemProps {
  item: ShowcaseItem;
  isSelected: boolean;
  onSelect: (id: string) => void;
  onUpdate?: (config: any) => void;
  onDelete: (id: string) => void;
  selectedElementId?: string | null;
  onElementSelect?: (elementId: string | null, elementType: 'text' | 'screenshot') => void;
}

const SortableShowcaseItem: React.FC<SortableShowcaseItemProps> = ({
  item,
  isSelected,
  onSelect,
  onUpdate,
  onDelete,
  selectedElementId,
  onElementSelect,
}) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: item.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className="flex-shrink-0 relative group"
    >
      {/* Drag Handle - only this area triggers showcase reordering */}
      <div
        {...attributes}
        {...listeners}
        className={`absolute top-2 left-2 z-20 p-1 bg-white/80 backdrop-blur-sm rounded shadow-sm ${
          isDragging ? 'cursor-grabbing' : 'cursor-grab'
        } hover:bg-white/90 transition-colors`}
        title="Drag to reorder showcases"
      >
        <GripVertical className="h-4 w-4 text-gray-600" />
      </div>

      {/* Delete Button - only visible on hover */}
      <button
        onClick={(e) => {
          e.stopPropagation();
          onDelete(item.id);
        }}
        className="absolute top-2 right-2 z-20 p-1 bg-red-500/80 backdrop-blur-sm rounded shadow-sm hover:bg-red-600/90 transition-colors opacity-0 group-hover:opacity-100"
        title="Delete showcase"
      >
        <Trash2 className="h-4 w-4 text-white" />
      </button>

      {/* Canvas Content - clicks here select the showcase but don't trigger drag */}
      <div
        onClick={(e) => {
          // Only select if not dragging and not clicking on interactive elements
          if (!isDragging) {
            onSelect(item.id);
          }
        }}
      >
        {isSelected && onUpdate ? (
          <InteractiveCanvas
            config={item.config}
            onUpdate={onUpdate}
            selectedElementId={selectedElementId}
            onElementSelect={onElementSelect}
          />
        ) : (
          <ShowcaseCanvas
            config={item.config}
            isSelected={isSelected}
            onClick={() => {}} // Handled by parent
          />
        )}
      </div>
    </div>
  );
};

interface ShowcaseGalleryProps {
  items: ShowcaseItem[];
  selectedId: string | null;
  onItemsChange: (items: ShowcaseItem[]) => void;
  onSelect: (id: string) => void;
  onAddShowcase: () => void;
  selectedElementId?: string | null;
  onElementSelect?: (elementId: string | null, elementType: 'text' | 'screenshot') => void;
}

export const ShowcaseGallery: React.FC<ShowcaseGalleryProps> = ({
  items,
  selectedId,
  onItemsChange,
  onSelect,
  onAddShowcase,
  selectedElementId,
  onElementSelect,
}) => {
  const [deleteConfirmId, setDeleteConfirmId] = useState<string | null>(null);
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8, // Only start drag after 8px movement
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (active.id !== over?.id) {
      const oldIndex = items.findIndex((item) => item.id === active.id);
      const newIndex = items.findIndex((item) => item.id === over?.id);

      onItemsChange(arrayMove(items, oldIndex, newIndex));
    }
  };

  const handleItemUpdate = (itemId: string, updatedConfig: any) => {
    const newItems = items.map(item =>
      item.id === itemId ? { ...item, config: updatedConfig } : item
    );
    onItemsChange(newItems);
  };

  const handleDeleteShowcase = (itemId: string) => {
    setDeleteConfirmId(itemId);
  };

  const confirmDelete = () => {
    if (deleteConfirmId) {
      const newItems = items.filter(item => item.id !== deleteConfirmId);
      onItemsChange(newItems);

      // If we deleted the selected item, select the first remaining item or null
      if (selectedId === deleteConfirmId) {
        onSelect(newItems.length > 0 ? newItems[0].id : '');
      }

      setDeleteConfirmId(null);
    }
  };

  const cancelDelete = () => {
    setDeleteConfirmId(null);
  };

  const itemIds = items.map((item) => item.id);

  return (
    <div className="bg-white rounded-lg shadow-lg h-full w-full">
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-gray-900">Showcase Gallery</h3>
            <p className="text-sm text-gray-600 mt-1">
              {items.length} showcase{items.length !== 1 ? 's' : ''} • Drag to reorder
            </p>
          </div>
          <button
            onClick={onAddShowcase}
            className="inline-flex items-center px-3 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors"
          >
            <Plus className="h-4 w-4 mr-1" />
            Add Showcase
          </button>
        </div>
      </div>

      <div className="p-4 overflow-x-auto">
        <DndContext
          sensors={sensors}
          collisionDetection={closestCenter}
          onDragEnd={handleDragEnd}
        >
          <SortableContext
            items={itemIds}
            strategy={horizontalListSortingStrategy}
          >
            <div className="flex space-x-4 pb-4">
              {items.map((item) => (
                <SortableShowcaseItem
                  key={item.id}
                  item={item}
                  isSelected={selectedId === item.id}
                  onSelect={onSelect}
                  onUpdate={(config) => handleItemUpdate(item.id, config)}
                  onDelete={handleDeleteShowcase}
                  selectedElementId={selectedElementId}
                  onElementSelect={onElementSelect}
                />
              ))}
            </div>
          </SortableContext>
        </DndContext>

        {items.length === 0 && (
          <div className="flex items-center justify-center h-64 text-gray-500">
            <div className="text-center">
              <div className="text-6xl mb-4">📱</div>
              <p className="text-lg font-medium">No showcases yet</p>
              <p className="text-sm text-gray-400 mt-1">
                Import screenshots or add a new showcase to get started
              </p>
            </div>
          </div>
        )}
      </div>

      {/* Delete Confirmation Dialog */}
      {deleteConfirmId && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-sm mx-4">
            <div className="flex items-center mb-4">
              <div className="flex-shrink-0">
                <Trash2 className="h-6 w-6 text-red-600" />
              </div>
              <div className="ml-3">
                <h3 className="text-lg font-medium text-gray-900">Delete Showcase</h3>
              </div>
            </div>
            <p className="text-sm text-gray-500 mb-6">
              Are you sure you want to delete this showcase? This action cannot be undone.
            </p>
            <div className="flex space-x-3">
              <button
                onClick={cancelDelete}
                className="flex-1 px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                Cancel
              </button>
              <button
                onClick={confirmDelete}
                className="flex-1 px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};