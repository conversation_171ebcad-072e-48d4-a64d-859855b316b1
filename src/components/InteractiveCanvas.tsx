import React, { useState, useRef, useCallback } from 'react';
import type { ShowcaseConfig, TextElement } from '../types/showcase.types';

interface InteractiveCanvasProps {
  config: ShowcaseConfig;
  onUpdate: (config: ShowcaseConfig) => void;
  selectedElementId?: string | null;
  onElementSelect?: (elementId: string | null, elementType: 'text' | 'screenshot') => void;
}

interface DragState {
  isDragging: boolean;
  elementId: string | null;
  elementType: 'text' | 'screenshot' | null;
  startX: number;
  startY: number;
  startElementX: number;
  startElementY: number;
}

interface ResizeState {
  isResizing: boolean;
  elementId: string | null;
  elementType: 'text' | 'screenshot' | null;
  handle: 'nw' | 'ne' | 'sw' | 'se' | 'n' | 's' | 'e' | 'w' | null;
  startX: number;
  startY: number;
  startWidth: number;
  startHeight: number;
  startFontSize?: number;
}

const generateTextShadow = (shadow: TextElement['shadow']): string => {
  if (!shadow.enabled) return 'none';
  const { offsetX, offsetY, blur, color, opacity } = shadow;
  const shadowColor = color + Math.round(opacity * 255).toString(16).padStart(2, '0');
  return `${offsetX}px ${offsetY}px ${blur}px ${shadowColor}`;
};

const generateBoxShadow = (shadow: ShowcaseConfig['screenshot']['shadow']): string => {
  if (!shadow.enabled) return 'none';
  const { offsetX, offsetY, blur, color, opacity } = shadow;
  const shadowColor = color + Math.round(opacity * 255).toString(16).padStart(2, '0');
  return `${offsetX}px ${offsetY}px ${blur}px ${shadowColor}`;
};

export const InteractiveCanvas: React.FC<InteractiveCanvasProps> = ({
  config,
  onUpdate,
  selectedElementId,
  onElementSelect,
}) => {
  const canvasRef = useRef<HTMLDivElement>(null);
  const [dragState, setDragState] = useState<DragState>({
    isDragging: false,
    elementId: null,
    elementType: null,
    startX: 0,
    startY: 0,
    startElementX: 0,
    startElementY: 0,
  });

  const [resizeState, setResizeState] = useState<ResizeState>({
    isResizing: false,
    elementId: null,
    elementType: null,
    handle: null,
    startX: 0,
    startY: 0,
    startWidth: 0,
    startHeight: 0,
  });

  const getCanvasRect = useCallback(() => {
    return canvasRef.current?.getBoundingClientRect() || { left: 0, top: 0 };
  }, []);

  const handleMouseDown = useCallback((e: React.MouseEvent, elementId: string, elementType: 'text' | 'screenshot') => {
    e.preventDefault();
    e.stopPropagation();
    
    const rect = getCanvasRect();
    const element = elementType === 'text' 
      ? config.textElements.find(el => el.id === elementId)
      : config.screenshot;
    
    if (!element) return;

    onElementSelect?.(elementId, elementType);

    setDragState({
      isDragging: true,
      elementId,
      elementType,
      startX: e.clientX - rect.left,
      startY: e.clientY - rect.top,
      startElementX: element.x,
      startElementY: element.y,
    });
  }, [config, getCanvasRect, onElementSelect]);

  const handleMouseMove = useCallback((e: React.MouseEvent) => {
    if (!dragState.isDragging || !dragState.elementId) return;

    const rect = getCanvasRect();
    const currentX = e.clientX - rect.left;
    const currentY = e.clientY - rect.top;
    
    const deltaX = currentX - dragState.startX;
    const deltaY = currentY - dragState.startY;
    
    const newX = Math.max(0, Math.min(1080, dragState.startElementX + deltaX * 4)); // Scale up for full resolution
    const newY = Math.max(0, Math.min(1920, dragState.startElementY + deltaY * 4)); // Scale up for full resolution

    if (dragState.elementType === 'text') {
      const newTextElements = config.textElements.map(el =>
        el.id === dragState.elementId ? { ...el, x: newX, y: newY } : el
      );
      onUpdate({ ...config, textElements: newTextElements });
    } else if (dragState.elementType === 'screenshot') {
      onUpdate({
        ...config,
        screenshot: { ...config.screenshot, x: newX, y: newY }
      });
    }
  }, [dragState, config, onUpdate, getCanvasRect]);

  const handleMouseUp = useCallback(() => {
    setDragState({
      isDragging: false,
      elementId: null,
      elementType: null,
      startX: 0,
      startY: 0,
      startElementX: 0,
      startElementY: 0,
    });
  }, []);

  const handleCanvasClick = useCallback((e: React.MouseEvent) => {
    if (e.target === canvasRef.current) {
      onElementSelect?.(null, 'text');
    }
  }, [onElementSelect]);

  return (
    <div
      ref={canvasRef}
      className="relative bg-gray-100 rounded-lg shadow-lg overflow-hidden cursor-crosshair"
      style={{
        width: '270px',
        height: '480px',
        background: config.background.type === 'solid' 
          ? config.background.value 
          : config.background.value || 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
      }}
      onMouseMove={handleMouseMove}
      onMouseUp={handleMouseUp}
      onMouseLeave={handleMouseUp}
      onClick={handleCanvasClick}
    >
      {/* Text Elements */}
      {config.textElements.map((textElement) => {
        const isSelected = selectedElementId === textElement.id;
        return (
          <div
            key={textElement.id}
            className={`absolute whitespace-pre-wrap cursor-move select-none ${
              isSelected ? 'ring-2 ring-blue-500' : ''
            }`}
            style={{
              color: textElement.color,
              fontSize: Math.min(textElement.fontSize * 0.25, 48), // Scale down for preview
              fontFamily: textElement.fontFamily,
              top: `${textElement.y * 0.25}px`, // Scale down for preview
              left: `${textElement.x * 0.25}px`, // Scale down for preview
              transform: `translate(-50%, -50%) rotate(${textElement.rotation}deg)`,
              transformOrigin: 'center',
              textShadow: generateTextShadow(textElement.shadow),
              zIndex: isSelected ? 10 : 1,
            }}
            onMouseDown={(e) => handleMouseDown(e, textElement.id, 'text')}
          >
            {textElement.content}
            
            {/* Selection handles for text */}
            {isSelected && (
              <>
                <div className="absolute -top-1 -left-1 w-2 h-2 bg-blue-500 rounded-full cursor-nw-resize" />
                <div className="absolute -top-1 -right-1 w-2 h-2 bg-blue-500 rounded-full cursor-ne-resize" />
                <div className="absolute -bottom-1 -left-1 w-2 h-2 bg-blue-500 rounded-full cursor-sw-resize" />
                <div className="absolute -bottom-1 -right-1 w-2 h-2 bg-blue-500 rounded-full cursor-se-resize" />
              </>
            )}
          </div>
        );
      })}

      {/* Screenshot */}
      {config.screenshotUrl && (
        <div
          className={`absolute cursor-move ${
            selectedElementId === 'screenshot' ? 'ring-2 ring-blue-500' : ''
          }`}
          style={{
            left: `${config.screenshot.x * 0.25}px`, // Scale down for preview
            top: `${config.screenshot.y * 0.25}px`, // Scale down for preview
            transform: `translate(-50%, -50%) rotate(${config.screenshot.rotation}deg)`,
            transformOrigin: 'center',
            zIndex: selectedElementId === 'screenshot' ? 10 : 1,
          }}
          onMouseDown={(e) => handleMouseDown(e, 'screenshot', 'screenshot')}
        >
          <img
            src={config.screenshotUrl}
            alt="Screenshot"
            className="rounded-lg"
            style={{
              width: Math.min(config.screenshot.width * 0.25, 200), // Scale down for preview
              height: Math.min(config.screenshot.height * 0.25, 356), // Scale down for preview
              borderRadius: config.screenshot.borderRadius * 0.25,
              objectFit: 'cover',
              boxShadow: generateBoxShadow(config.screenshot.shadow),
            }}
            draggable={false}
          />
          
          {/* Selection handles for screenshot */}
          {selectedElementId === 'screenshot' && (
            <>
              <div className="absolute -top-1 -left-1 w-2 h-2 bg-blue-500 rounded-full cursor-nw-resize" />
              <div className="absolute -top-1 -right-1 w-2 h-2 bg-blue-500 rounded-full cursor-ne-resize" />
              <div className="absolute -bottom-1 -left-1 w-2 h-2 bg-blue-500 rounded-full cursor-sw-resize" />
              <div className="absolute -bottom-1 -right-1 w-2 h-2 bg-blue-500 rounded-full cursor-se-resize" />
            </>
          )}
        </div>
      )}
    </div>
  );
};
