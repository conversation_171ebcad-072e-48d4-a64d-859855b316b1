import React from 'react';
import type { ShowcaseConfig, TextElement } from '../types/showcase.types';

interface ShowcaseCanvasProps {
  config: ShowcaseConfig;
  onClick?: () => void;
  isSelected?: boolean;
}

const generateTextShadow = (shadow: TextElement['shadow']): string => {
  if (!shadow.enabled) return 'none';
  const { offsetX, offsetY, blur, color, opacity } = shadow;
  const shadowColor = color + Math.round(opacity * 255).toString(16).padStart(2, '0');
  return `${offsetX}px ${offsetY}px ${blur}px ${shadowColor}`;
};

const generateBoxShadow = (shadow: ShowcaseConfig['screenshot']['shadow']): string => {
  if (!shadow.enabled) return 'none';
  const { offsetX, offsetY, blur, color, opacity } = shadow;
  const shadowColor = color + Math.round(opacity * 255).toString(16).padStart(2, '0');
  return `${offsetX}px ${offsetY}px ${blur}px ${shadowColor}`;
};

export const ShowcaseCanvas: React.FC<ShowcaseCanvasProps> = ({
  config,
  onClick,
  isSelected = false,
}) => {
  return (
    <div
      className={`relative cursor-pointer transition-all duration-200 ${
        isSelected 
          ? 'ring-4 ring-blue-500 ring-offset-2 scale-105' 
          : 'hover:ring-2 hover:ring-blue-300 hover:scale-102'
      }`}
      onClick={onClick}
    >
      <div
        className="bg-gray-100 rounded-lg shadow-lg relative overflow-hidden"
        style={{
          width: '270px',
          height: '480px',
          background: config.background.type === 'solid'
            ? config.background.value
            : config.background.value || 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
        }}
      >
        {/* Text Elements */}
        {config.textElements.map((textElement) => (
          <div
            key={textElement.id}
            className="absolute whitespace-pre-wrap"
            style={{
              color: textElement.color,
              fontSize: Math.min(textElement.fontSize * 0.25, 48), // Scale down for preview
              fontFamily: textElement.fontFamily,
              top: `${textElement.y * 0.25}px`, // Scale down for preview
              left: `${textElement.x * 0.25}px`, // Scale down for preview
              transform: `translate(-50%, -50%) rotate(${textElement.rotation}deg)`,
              textShadow: generateTextShadow(textElement.shadow),
              transformOrigin: 'center',
            }}
          >
            {textElement.content}
          </div>
        ))}

        {/* Screenshot positioned absolutely using x/y coordinates */}
        {config.screenshotUrl && (
          <img
            src={config.screenshotUrl}
            alt="Screenshot"
            className="absolute rounded-lg"
            style={{
              width: Math.min(config.screenshot.width * 0.25, 200), // Scale down for preview
              height: Math.min(config.screenshot.height * 0.25, 356), // Scale down for preview
              borderRadius: config.screenshot.borderRadius * 0.25,
              objectFit: 'cover',
              left: `${config.screenshot.x * 0.25}px`, // Scale down x position for preview
              top: `${config.screenshot.y * 0.25}px`, // Scale down y position for preview
              transform: `translate(-50%, -50%) rotate(${config.screenshot.rotation}deg)`, // Center and rotate
              transformOrigin: 'center',
              boxShadow: generateBoxShadow(config.screenshot.shadow),
            }}
          />
        )}
      </div>
    </div>
  );
};