import React, { useState } from 'react';
import { Settings, Type, Image, Palette, Plus, Minus } from 'lucide-react';
import type { ShowcaseConfig, TextElement } from '../types/showcase.types';
import { generateGradientCSS, createDefaultTextElement } from '../utils/canvasHelpers';

interface EditorPanelProps {
  config: ShowcaseConfig;
  onUpdate: (config: ShowcaseConfig) => void;
  activeTab: string;
  onTabChange: (tab: string) => void;
}

const tabs = [
  { id: 'text', label: 'Text', icon: Type },
  { id: 'screenshot', label: 'Screenshot', icon: Image },
  { id: 'background', label: 'Background', icon: Palette },
];

export const EditorPanel: React.FC<EditorPanelProps> = ({
  config,
  onUpdate,
  activeTab,
  onTabChange,
}) => {
  const [selectedTextElementId, setSelectedTextElementId] = useState<string | null>(
    config.textElements.length > 0 ? config.textElements[0].id : null
  );
  const handleTextChange = (field: string, value: string | number) => {
    onUpdate({
      ...config,
      text: { ...config.text, [field]: value },
    });
  };

  const handleScreenshotChange = (field: string, value: number | boolean) => {
    onUpdate({
      ...config,
      screenshot: { ...config.screenshot, [field]: value },
    });
  };

  const handleScreenshotShadowChange = (field: string, value: number | boolean | string) => {
    onUpdate({
      ...config,
      screenshot: {
        ...config.screenshot,
        shadow: { ...config.screenshot.shadow, [field]: value }
      },
    });
  };

  const addTextElement = () => {
    const newElement = createDefaultTextElement('New Text', 540, 400);
    onUpdate({
      ...config,
      textElements: [...config.textElements, newElement],
    });
    setSelectedTextElementId(newElement.id);
  };

  const removeTextElement = (elementId: string) => {
    const newElements = config.textElements.filter(el => el.id !== elementId);
    onUpdate({
      ...config,
      textElements: newElements,
    });
    if (selectedTextElementId === elementId) {
      setSelectedTextElementId(newElements.length > 0 ? newElements[0].id : null);
    }
  };

  const updateTextElement = (elementId: string, updates: Partial<TextElement>) => {
    const newElements = config.textElements.map(el =>
      el.id === elementId ? { ...el, ...updates } : el
    );
    onUpdate({
      ...config,
      textElements: newElements,
    });
  };

  const updateTextElementShadow = (elementId: string, field: string, value: number | boolean | string) => {
    const newElements = config.textElements.map(el =>
      el.id === elementId
        ? { ...el, shadow: { ...el.shadow, [field]: value } }
        : el
    );
    onUpdate({
      ...config,
      textElements: newElements,
    });
  };

  const handleBackgroundChange = (type: 'solid' | 'gradient', value: string, gradient?: any) => {
    const newBackground = { type, value, ...(gradient && { gradient }) };
    onUpdate({
      ...config,
      background: newBackground,
    });
  };

  const handleGradientChange = (gradientUpdate: Partial<NonNullable<ShowcaseConfig['background']['gradient']>>) => {
    const currentGradient = config.background.gradient || {
      type: 'linear' as const,
      angle: 135,
      colors: [
        { color: '#667eea', position: 0 },
        { color: '#764ba2', position: 100 }
      ]
    };

    const newGradient = { ...currentGradient, ...gradientUpdate };
    const gradientCSS = generateGradientCSS(newGradient);

    handleBackgroundChange('gradient', gradientCSS, newGradient);
  };

  const renderTextControls = () => {
    const selectedElement = config.textElements.find(el => el.id === selectedTextElementId);

    return (
      <div className="space-y-4">
        {/* Text Elements List */}
        <div>
          <div className="flex items-center justify-between mb-2">
            <label className="block text-sm font-medium text-gray-700">Text Elements</label>
            <button
              onClick={addTextElement}
              className="p-1 text-blue-600 hover:text-blue-800"
              title="Add text element"
            >
              <Plus className="h-4 w-4" />
            </button>
          </div>

          <div className="space-y-2 max-h-32 overflow-y-auto">
            {config.textElements.map((element, index) => (
              <div
                key={element.id}
                className={`flex items-center justify-between p-2 border rounded cursor-pointer ${
                  selectedTextElementId === element.id
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
                onClick={() => setSelectedTextElementId(element.id)}
              >
                <span className="text-sm truncate flex-1">
                  {element.content || `Text ${index + 1}`}
                </span>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    removeTextElement(element.id);
                  }}
                  className="p-1 text-red-600 hover:text-red-800 ml-2"
                  title="Remove text element"
                >
                  <Minus className="h-3 w-3" />
                </button>
              </div>
            ))}
          </div>
        </div>

        {/* Selected Element Controls */}
        {selectedElement && (
          <div className="space-y-4 pt-4 border-t border-gray-200">
            <h4 className="text-sm font-medium text-gray-700">Edit Text Element</h4>

            {/* Content */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Content</label>
              <textarea
                value={selectedElement.content}
                onChange={(e) => updateTextElement(selectedElement.id, { content: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                rows={2}
              />
            </div>

            {/* Font Size */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Font Size: {selectedElement.fontSize}px
              </label>
              <input
                type="range"
                min="12"
                max="120"
                value={selectedElement.fontSize}
                onChange={(e) => updateTextElement(selectedElement.id, { fontSize: Number(e.target.value) })}
                className="w-full"
              />
            </div>

            {/* Color */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Color</label>
              <input
                type="color"
                value={selectedElement.color}
                onChange={(e) => updateTextElement(selectedElement.id, { color: e.target.value })}
                className="w-full h-10 rounded-md cursor-pointer"
              />
            </div>

            {/* Position */}
            <div>
              <h5 className="text-sm font-medium text-gray-700 mb-2">Position</h5>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-xs font-medium text-gray-600 mb-1">X: {selectedElement.x}px</label>
                  <input
                    type="range"
                    min="0"
                    max="1080"
                    value={selectedElement.x}
                    onChange={(e) => updateTextElement(selectedElement.id, { x: Number(e.target.value) })}
                    className="w-full"
                  />
                </div>
                <div>
                  <label className="block text-xs font-medium text-gray-600 mb-1">Y: {selectedElement.y}px</label>
                  <input
                    type="range"
                    min="0"
                    max="1920"
                    value={selectedElement.y}
                    onChange={(e) => updateTextElement(selectedElement.id, { y: Number(e.target.value) })}
                    className="w-full"
                  />
                </div>
              </div>
            </div>

            {/* Rotation */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Rotation: {selectedElement.rotation}°
              </label>
              <input
                type="range"
                min="0"
                max="360"
                value={selectedElement.rotation}
                onChange={(e) => updateTextElement(selectedElement.id, { rotation: Number(e.target.value) })}
                className="w-full"
              />
            </div>

            {/* Shadow Controls */}
            <div>
              <div className="flex items-center justify-between mb-2">
                <label className="block text-sm font-medium text-gray-700">Text Shadow</label>
                <input
                  type="checkbox"
                  checked={selectedElement.shadow.enabled}
                  onChange={(e) => updateTextElementShadow(selectedElement.id, 'enabled', e.target.checked)}
                  className="rounded"
                />
              </div>

              {selectedElement.shadow.enabled && (
                <div className="space-y-3 pl-4 border-l-2 border-gray-200">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-xs font-medium text-gray-600 mb-1">
                        Offset X: {selectedElement.shadow.offsetX}px
                      </label>
                      <input
                        type="range"
                        min="-20"
                        max="20"
                        value={selectedElement.shadow.offsetX}
                        onChange={(e) => updateTextElementShadow(selectedElement.id, 'offsetX', Number(e.target.value))}
                        className="w-full"
                      />
                    </div>
                    <div>
                      <label className="block text-xs font-medium text-gray-600 mb-1">
                        Offset Y: {selectedElement.shadow.offsetY}px
                      </label>
                      <input
                        type="range"
                        min="-20"
                        max="20"
                        value={selectedElement.shadow.offsetY}
                        onChange={(e) => updateTextElementShadow(selectedElement.id, 'offsetY', Number(e.target.value))}
                        className="w-full"
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-xs font-medium text-gray-600 mb-1">
                      Blur: {selectedElement.shadow.blur}px
                    </label>
                    <input
                      type="range"
                      min="0"
                      max="20"
                      value={selectedElement.shadow.blur}
                      onChange={(e) => updateTextElementShadow(selectedElement.id, 'blur', Number(e.target.value))}
                      className="w-full"
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-xs font-medium text-gray-600 mb-1">Shadow Color</label>
                      <input
                        type="color"
                        value={selectedElement.shadow.color}
                        onChange={(e) => updateTextElementShadow(selectedElement.id, 'color', e.target.value)}
                        className="w-full h-8 rounded cursor-pointer"
                      />
                    </div>
                    <div>
                      <label className="block text-xs font-medium text-gray-600 mb-1">
                        Opacity: {Math.round(selectedElement.shadow.opacity * 100)}%
                      </label>
                      <input
                        type="range"
                        min="0"
                        max="1"
                        step="0.1"
                        value={selectedElement.shadow.opacity}
                        onChange={(e) => updateTextElementShadow(selectedElement.id, 'opacity', Number(e.target.value))}
                        className="w-full"
                      />
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    );
  };

  const renderScreenshotControls = () => (
    <div className="space-y-4">
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">Width</label>
        <input
          type="range"
          min="400"
          max="1000"
          value={config.screenshot.width}
          onChange={(e) => handleScreenshotChange('width', Number(e.target.value))}
          className="w-full"
        />
        <span className="text-sm text-gray-500">{config.screenshot.width}px</span>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">Height</label>
        <input
          type="range"
          min="600"
          max="1600"
          value={config.screenshot.height}
          onChange={(e) => handleScreenshotChange('height', Number(e.target.value))}
          className="w-full"
        />
        <span className="text-sm text-gray-500">{config.screenshot.height}px</span>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">Border Radius</label>
        <input
          type="range"
          min="0"
          max="80"
          value={config.screenshot.borderRadius}
          onChange={(e) => handleScreenshotChange('borderRadius', Number(e.target.value))}
          className="w-full"
        />
        <span className="text-sm text-gray-500">{config.screenshot.borderRadius}px</span>
      </div>

      <div className="pt-2 border-t border-gray-200">
        <h4 className="text-sm font-medium text-gray-700 mb-3">Position</h4>
        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-xs font-medium text-gray-600 mb-1">X Position</label>
            <input
              type="range"
              min="0"
              max="1080"
              value={config.screenshot.x}
              onChange={(e) => handleScreenshotChange('x', Number(e.target.value))}
              className="w-full"
            />
            <span className="text-xs text-gray-500">{config.screenshot.x}px</span>
          </div>
          <div>
            <label className="block text-xs font-medium text-gray-600 mb-1">Y Position</label>
            <input
              type="range"
              min="0"
              max="1920"
              value={config.screenshot.y}
              onChange={(e) => handleScreenshotChange('y', Number(e.target.value))}
              className="w-full"
            />
            <span className="text-xs text-gray-500">{config.screenshot.y}px</span>
          </div>
        </div>
      </div>

      {/* Rotation */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Rotation: {config.screenshot.rotation}°
        </label>
        <input
          type="range"
          min="0"
          max="360"
          value={config.screenshot.rotation}
          onChange={(e) => handleScreenshotChange('rotation', Number(e.target.value))}
          className="w-full"
        />
      </div>

      {/* Shadow Controls */}
      <div className="pt-4 border-t border-gray-200">
        <div className="flex items-center justify-between mb-2">
          <label className="block text-sm font-medium text-gray-700">Drop Shadow</label>
          <input
            type="checkbox"
            checked={config.screenshot.shadow.enabled}
            onChange={(e) => handleScreenshotShadowChange('enabled', e.target.checked)}
            className="rounded"
          />
        </div>

        {config.screenshot.shadow.enabled && (
          <div className="space-y-3 pl-4 border-l-2 border-gray-200">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-xs font-medium text-gray-600 mb-1">
                  Offset X: {config.screenshot.shadow.offsetX}px
                </label>
                <input
                  type="range"
                  min="-50"
                  max="50"
                  value={config.screenshot.shadow.offsetX}
                  onChange={(e) => handleScreenshotShadowChange('offsetX', Number(e.target.value))}
                  className="w-full"
                />
              </div>
              <div>
                <label className="block text-xs font-medium text-gray-600 mb-1">
                  Offset Y: {config.screenshot.shadow.offsetY}px
                </label>
                <input
                  type="range"
                  min="-50"
                  max="50"
                  value={config.screenshot.shadow.offsetY}
                  onChange={(e) => handleScreenshotShadowChange('offsetY', Number(e.target.value))}
                  className="w-full"
                />
              </div>
            </div>

            <div>
              <label className="block text-xs font-medium text-gray-600 mb-1">
                Blur: {config.screenshot.shadow.blur}px
              </label>
              <input
                type="range"
                min="0"
                max="50"
                value={config.screenshot.shadow.blur}
                onChange={(e) => handleScreenshotShadowChange('blur', Number(e.target.value))}
                className="w-full"
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-xs font-medium text-gray-600 mb-1">Shadow Color</label>
                <input
                  type="color"
                  value={config.screenshot.shadow.color}
                  onChange={(e) => handleScreenshotShadowChange('color', e.target.value)}
                  className="w-full h-8 rounded cursor-pointer"
                />
              </div>
              <div>
                <label className="block text-xs font-medium text-gray-600 mb-1">
                  Opacity: {Math.round(config.screenshot.shadow.opacity * 100)}%
                </label>
                <input
                  type="range"
                  min="0"
                  max="1"
                  step="0.1"
                  value={config.screenshot.shadow.opacity}
                  onChange={(e) => handleScreenshotShadowChange('opacity', Number(e.target.value))}
                  className="w-full"
                />
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );

  const renderBackgroundControls = () => {
    const currentGradient = config.background.gradient || {
      type: 'linear' as const,
      angle: 135,
      colors: [
        { color: '#667eea', position: 0 },
        { color: '#764ba2', position: 100 }
      ]
    };

    const addGradientColor = () => {
      const newColors = [...currentGradient.colors, { color: '#ffffff', position: 50 }];
      handleGradientChange({ colors: newColors });
    };

    const removeGradientColor = (index: number) => {
      if (currentGradient.colors.length > 2) {
        const newColors = currentGradient.colors.filter((_, i) => i !== index);
        handleGradientChange({ colors: newColors });
      }
    };

    const updateGradientColor = (index: number, field: 'color' | 'position', value: string | number) => {
      const newColors = currentGradient.colors.map((color, i) =>
        i === index ? { ...color, [field]: value } : color
      );
      handleGradientChange({ colors: newColors });
    };

    return (
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Background Type</label>
          <select
            value={config.background.type}
            onChange={(e) => {
              const newType = e.target.value as 'solid' | 'gradient';
              if (newType === 'gradient') {
                const gradientCSS = generateGradientCSS(currentGradient);
                handleBackgroundChange('gradient', gradientCSS, currentGradient);
              } else {
                handleBackgroundChange('solid', '#667eea');
              }
            }}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
          >
            <option value="solid">Solid Color</option>
            <option value="gradient">Gradient</option>
          </select>
        </div>

        {config.background.type === 'solid' && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Background Color</label>
            <input
              type="color"
              value={config.background.value}
              onChange={(e) => handleBackgroundChange('solid', e.target.value)}
              className="w-full h-10 rounded-md cursor-pointer"
            />
          </div>
        )}

        {config.background.type === 'gradient' && (
          <div className="space-y-4">
            {/* Gradient Preview */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Gradient Preview</label>
              <div
                className="w-full h-16 rounded-md border border-gray-300"
                style={{ background: config.background.value }}
              />
            </div>

            {/* Gradient Type */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Gradient Type</label>
              <select
                value={currentGradient.type}
                onChange={(e) => handleGradientChange({ type: e.target.value as 'linear' | 'radial' })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
              >
                <option value="linear">Linear</option>
                <option value="radial">Radial</option>
              </select>
            </div>

            {/* Gradient Angle (only for linear) */}
            {currentGradient.type === 'linear' && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Angle: {currentGradient.angle}°
                </label>
                <input
                  type="range"
                  min="0"
                  max="360"
                  value={currentGradient.angle}
                  onChange={(e) => handleGradientChange({ angle: Number(e.target.value) })}
                  className="w-full"
                />
              </div>
            )}

            {/* Gradient Colors */}
            <div>
              <div className="flex items-center justify-between mb-2">
                <label className="block text-sm font-medium text-gray-700">Colors</label>
                <button
                  onClick={addGradientColor}
                  className="p-1 text-blue-600 hover:text-blue-800"
                  title="Add color"
                >
                  <Plus className="h-4 w-4" />
                </button>
              </div>

              <div className="space-y-2">
                {currentGradient.colors.map((colorStop, index) => (
                  <div key={index} className="flex items-center space-x-2">
                    <input
                      type="color"
                      value={colorStop.color}
                      onChange={(e) => updateGradientColor(index, 'color', e.target.value)}
                      className="w-12 h-8 rounded cursor-pointer"
                    />
                    <input
                      type="range"
                      min="0"
                      max="100"
                      value={colorStop.position}
                      onChange={(e) => updateGradientColor(index, 'position', Number(e.target.value))}
                      className="flex-1"
                    />
                    <span className="text-xs text-gray-500 w-8">{colorStop.position}%</span>
                    {currentGradient.colors.length > 2 && (
                      <button
                        onClick={() => removeGradientColor(index)}
                        className="p-1 text-red-600 hover:text-red-800"
                        title="Remove color"
                      >
                        <Minus className="h-3 w-3" />
                      </button>
                    )}
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}
      </div>
    );
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case 'text':
        return renderTextControls();
      case 'screenshot':
        return renderScreenshotControls();
      case 'background':
        return renderBackgroundControls();
      default:
        return null;
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-lg h-full flex flex-col">
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center space-x-2">
          <Settings className="h-5 w-5 text-gray-500" />
          <h3 className="text-lg font-semibold text-gray-900">Editor</h3>
        </div>
      </div>

      <div className="flex border-b border-gray-200 overflow-x-auto">
        {tabs.map((tab) => {
          const Icon = tab.icon;
          return (
            <button
              key={tab.id}
              onClick={() => onTabChange(tab.id)}
              className={`flex-1 min-w-0 flex flex-col items-center justify-center px-2 py-3 text-xs font-medium transition-colors ${
                activeTab === tab.id
                  ? 'text-primary-600 border-b-2 border-primary-600'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              <Icon className="h-4 w-4 mb-1" />
              <span className="truncate">{tab.label}</span>
            </button>
          );
        })}
      </div>

      <div className="flex-1 p-4 overflow-y-auto">
        {renderTabContent()}
      </div>
    </div>
  );
};