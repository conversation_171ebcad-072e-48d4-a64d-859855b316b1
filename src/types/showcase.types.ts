export interface TextElement {
  id: string;
  content: string;
  x: number;
  y: number;
  fontSize: number;
  fontFamily: string;
  color: string;
  rotation: number;
  shadow: {
    enabled: boolean;
    offsetX: number;
    offsetY: number;
    blur: number;
    color: string;
    opacity: number;
  };
}

export interface ShowcaseConfig {
  id: string;
  screenshotUrl: string;
  // Legacy fields for backward compatibility
  title: string;
  subtitle: string;
  background: {
    type: 'solid' | 'gradient';
    value: string;
    gradient?: {
      type: 'linear' | 'radial';
      angle: number;
      colors: Array<{
        color: string;
        position: number;
      }>;
    };
  };
  // Legacy text config for backward compatibility
  text: {
    fontSize: number;
    fontFamily: string;
    color: string;
    titleX: number;
    titleY: number;
    subtitleX: number;
    subtitleY: number;
  };
  // New text elements system
  textElements: TextElement[];
  screenshot: {
    x: number;
    y: number;
    width: number;
    height: number;
    borderRadius: number;
    rotation: number;
    shadow: {
      enabled: boolean;
      offsetX: number;
      offsetY: number;
      blur: number;
      color: string;
      opacity: number;
    };
  };
}

export interface ShowcaseItem {
  id: string;
  config: ShowcaseConfig;
  canvas?: HTMLCanvasElement;
}

export interface EditorState {
  selectedId: string | null;
  activeTab: 'text' | 'screenshot' | 'background';
}

export interface ExportOptions {
  format: 'png' | 'jpg';
  quality: number;
  scale: number;
}