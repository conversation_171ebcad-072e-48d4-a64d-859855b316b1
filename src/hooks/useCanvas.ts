import { useRef, useEffect, useState } from 'react';
import * as fabric from 'fabric';
import type { ShowcaseConfig } from '../types/showcase.types';

export const useCanvas = (config: ShowcaseConfig) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const fabricRef = useRef<fabric.Canvas>(null);
  const [isReady, setIsReady] = useState(false);

  useEffect(() => {
    if (!canvasRef.current) return;

    // Initialize Fabric.js canvas
    const canvas = new fabric.Canvas(canvasRef.current, {
      width: 1080,
      height: 1920,
      backgroundColor: config.background.type === 'solid' 
        ? config.background.value 
        : '#ffffff',
    });

    fabricRef.current = canvas;
    setIsReady(true);

    return () => {
      canvas.dispose();
    };
  }, []);

  useEffect(() => {
    if (!fabricRef.current || !isReady) return;

    const canvas = fabricRef.current;
    canvas.clear();

    // Set background
    if (config.background.type === 'gradient') {
      canvas.backgroundColor = '#667eea';
    } else {
      canvas.backgroundColor = config.background.value;
    }

    // Add title text
    const titleText = new fabric.Text(config.title, {
      left: 540,
      top: config.text.titleY,
      fontSize: config.text.fontSize,
      fontFamily: config.text.fontFamily,
      fill: config.text.color,
      originX: 'center',
      originY: 'center',
      fontWeight: 'bold',
    });

    // Add subtitle text
    const subtitleText = new fabric.Text(config.subtitle, {
      left: 540,
      top: config.text.subtitleY,
      fontSize: config.text.fontSize * 0.7,
      fontFamily: config.text.fontFamily,
      fill: config.text.color,
      originX: 'center',
      originY: 'center',
    });

    // Add screenshot
    if (config.screenshotUrl) {
      // Skip fabric.js for now - using html2canvas instead
      canvas.add(titleText);
      canvas.add(subtitleText);
      canvas.renderAll();
    } else {
      canvas.add(titleText);
      canvas.add(subtitleText);
      canvas.renderAll();
    }

  }, [config, isReady]);

  const exportCanvas = async (format: 'png' | 'jpg' = 'png', quality = 1) => {
    if (!fabricRef.current) return null;
    
    return new Promise<string>((resolve) => {
      const dataURL = fabricRef.current!.toDataURL({
        format: format === 'jpg' ? 'jpeg' : format,
        quality,
        multiplier: 2, // High resolution
      });
      resolve(dataURL);
    });
  };

  return {
    canvasRef,
    fabricRef,
    isReady,
    exportCanvas,
  };
};