import { useState, useRef } from 'react';
import { TopNavigation } from './components/TopNavigation';
import { ImageUploader } from './components/ImageUploader';
import { ShowcaseGallery } from './components/ShowcaseGallery';
import { EditorPanel } from './components/EditorPanel';
import type { ShowcaseItem, ShowcaseConfig } from './types/showcase.types';
import { createDefaultShowcase } from './utils/canvasHelpers';
import html2canvas from 'html2canvas';
import { downloadImage } from './utils/canvasHelpers';

function App() {
  const [showcases, setShowcases] = useState<ShowcaseItem[]>([]);
  const [selectedId, setSelectedId] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('text');
  const [isLoading, setIsLoading] = useState(false);
  const [selectedElementId, setSelectedElementId] = useState<string | null>(null);
  const [selectedElementType, setSelectedElementType] = useState<'text' | 'screenshot'>('text');

  const fileInputRef = useRef<HTMLInputElement>(null);
  const selectedShowcase = showcases.find(s => s.id === selectedId);

  const handleImagesLoaded = (files: File[]) => {
    const newShowcases = files.map(file => {
      const url = URL.createObjectURL(file);
      const config = createDefaultShowcase(url);
      return {
        id: config.id,
        config,
      };
    });

    setShowcases(prev => [...prev, ...newShowcases]);
    if (newShowcases.length > 0 && !selectedId) {
      setSelectedId(newShowcases[0].id);
    }
  };

  const handleUpdateShowcase = (updatedConfig: ShowcaseConfig) => {
    setShowcases(prev =>
      prev.map(item =>
        item.id === selectedId
          ? { ...item, config: updatedConfig }
          : item
      )
    );
  };

  const handleElementSelect = (elementId: string | null, elementType: 'text' | 'screenshot') => {
    setSelectedElementId(elementId);
    setSelectedElementType(elementType);
  };

  const handleAddShowcase = () => {
    const config = createDefaultShowcase('');
    const newShowcase: ShowcaseItem = {
      id: config.id,
      config,
    };

    setShowcases(prev => [...prev, newShowcase]);
    setSelectedId(newShowcase.id);
  };

  const handleImport = () => {
    fileInputRef.current?.click();
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files && files.length > 0) {
      const fileArray = Array.from(files);
      handleImagesLoaded(fileArray);
    }
    // Reset the input so the same file can be selected again
    if (event.target) {
      event.target.value = '';
    }
  };


  const handleExport = async () => {
    if (showcases.length === 0) return;

    setIsLoading(true);
    
    try {
      for (let i = 0; i < showcases.length; i++) {
        const showcase = showcases[i];
        const canvas = document.createElement('canvas');
        canvas.width = 1080;
        canvas.height = 1920;
        
        // Create temporary container for canvas
        const tempContainer = document.createElement('div');
        tempContainer.style.position = 'absolute';
        tempContainer.style.left = '-9999px';
        tempContainer.style.width = '1080px';
        tempContainer.style.height = '1920px';
        document.body.appendChild(tempContainer);

        // Create showcase content
        const showcaseDiv = document.createElement('div');
        showcaseDiv.style.width = '1080px';
        showcaseDiv.style.height = '1920px';
        showcaseDiv.style.position = 'relative';
        showcaseDiv.style.background = showcase.config.background.value;
        showcaseDiv.style.display = 'flex';
        showcaseDiv.style.flexDirection = 'column';
        showcaseDiv.style.alignItems = 'center';
        showcaseDiv.style.justifyContent = 'center';
        showcaseDiv.style.padding = '100px';
        
        // Add text elements
        const textElements: HTMLElement[] = [];
        showcase.config.textElements.forEach(textElement => {
          const textDiv = document.createElement('div');
          textDiv.textContent = textElement.content;
          textDiv.style.fontSize = `${textElement.fontSize}px`;
          textDiv.style.fontFamily = textElement.fontFamily;
          textDiv.style.color = textElement.color;
          textDiv.style.position = 'absolute';
          textDiv.style.left = `${textElement.x}px`;
          textDiv.style.top = `${textElement.y}px`;
          textDiv.style.transform = `translate(-50%, -50%) rotate(${textElement.rotation}deg)`;
          textDiv.style.transformOrigin = 'center';
          textDiv.style.whiteSpace = 'pre-wrap';

          // Add text shadow if enabled
          if (textElement.shadow.enabled) {
            const { offsetX, offsetY, blur, color, opacity } = textElement.shadow;
            const shadowColor = color + Math.round(opacity * 255).toString(16).padStart(2, '0');
            textDiv.style.textShadow = `${offsetX}px ${offsetY}px ${blur}px ${shadowColor}`;
          }

          textElements.push(textDiv);
        });
        
        // Add text elements to showcase
        textElements.forEach(textDiv => {
          showcaseDiv.appendChild(textDiv);
        });

        // Add screenshot with positioning
        if (showcase.config.screenshotUrl && showcase.config.screenshot) {
          const img = document.createElement('img');
          img.src = showcase.config.screenshotUrl;
          img.style.width = `${showcase.config.screenshot.width || 800}px`;
          img.style.height = `${showcase.config.screenshot.height || 1422}px`;
          img.style.borderRadius = `${showcase.config.screenshot.borderRadius || 40}px`;
          img.style.objectFit = 'cover';
          img.style.position = 'absolute';
          img.style.left = `${showcase.config.screenshot.x || 540}px`;
          img.style.top = `${showcase.config.screenshot.y || 1100}px`;
          img.style.transform = `translate(-50%, -50%) rotate(${showcase.config.screenshot.rotation || 0}deg)`;
          img.style.transformOrigin = 'center';

          // Add box shadow if enabled
          if (showcase.config.screenshot.shadow?.enabled) {
            const { offsetX, offsetY, blur, color, opacity } = showcase.config.screenshot.shadow;
            const shadowColor = color + Math.round(opacity * 255).toString(16).padStart(2, '0');
            img.style.boxShadow = `${offsetX}px ${offsetY}px ${blur}px ${shadowColor}`;
          }

          // Add error handling for image loading
          img.onerror = () => {
            console.warn('Failed to load screenshot image:', showcase.config.screenshotUrl);
          };

          showcaseDiv.appendChild(img);
        }

        tempContainer.appendChild(showcaseDiv);

        // Convert to canvas and download
        const canvasResult = await html2canvas(showcaseDiv, {
          width: 1080,
          height: 1920,
          backgroundColor: null,
          scale: 2,
        });

        const dataURL = canvasResult.toDataURL('image/png', 1);
        downloadImage(dataURL, `showcase-${i + 1}.png`);

        document.body.removeChild(tempContainer);
      }
    } catch (error) {
      console.error('Export failed:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hidden file input for import functionality */}
      <input
        ref={fileInputRef}
        type="file"
        multiple
        accept="image/*"
        onChange={handleFileSelect}
        className="hidden"
      />

      <TopNavigation
        onImport={handleImport}
        onExport={handleExport}
        importDisabled={isLoading}
        exportDisabled={isLoading || showcases.length === 0}
      />

      <div className="max-w-7xl mx-auto px-6 py-8">
        {showcases.length === 0 ? (
          <div className="flex justify-center">
            <div className="w-full max-w-2xl">
              <ImageUploader onImagesLoaded={handleImagesLoaded} />
            </div>
          </div>
        ) : (
          <div className="relative">
            {/* Main content area - uses calc to subtract editor width and gaps */}
            <div style={{ width: 'calc(100% - 336px)' }}>
              <ShowcaseGallery
                items={showcases}
                selectedId={selectedId}
                onItemsChange={setShowcases}
                onSelect={setSelectedId}
                onAddShowcase={handleAddShowcase}
                selectedElementId={selectedElementId}
                onElementSelect={handleElementSelect}
              />
            </div>

            {/* Fixed Editor Panel on the right */}
            <div className="fixed top-20 right-6 w-80 h-[calc(100vh-6rem)] z-10">
              {selectedShowcase && (
                <EditorPanel
                  config={selectedShowcase.config}
                  onUpdate={handleUpdateShowcase}
                  activeTab={activeTab}
                  onTabChange={setActiveTab}
                />
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

export default App;
