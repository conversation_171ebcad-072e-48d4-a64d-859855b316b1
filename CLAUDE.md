d# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a React + TypeScript + Vite project named "screenshots2show" - a minimal setup with HMR (Hot Module Replacement) and ESLint rules. The project uses modern tooling with strict TypeScript configuration.

## Development Commands

- **Start development server**: `npm run dev` - runs Vite dev server with HMR
- **Build for production**: `npm run build` - compiles TypeScript and builds with Vite
- **Lint code**: `npm run lint` - runs ESLint across the codebase
- **Preview production build**: `npm run preview` - serves the built application locally

## Architecture & Structure

- **Build Tool**: Vite with React plugin (@vitejs/plugin-react)
- **Language**: TypeScript with strict configuration
- **Styling**: CSS modules with basic global styles in `src/index.css`
- **Linting**: ESLint with TypeScript, React hooks, and React refresh plugins
- **Target**: ES2022 modules, modern browsers

## Key Files

- `src/main.tsx` - Application entry point using React 18's createRoot
- `src/App.tsx` - Main application component with basic counter example
- `vite.config.ts` - Vite configuration with React plugin
- `eslint.config.js` - ESLint configuration with TypeScript and React rules
- `tsconfig.app.json` - TypeScript configuration for application code
- `tsconfig.node.json` - TypeScript configuration for Node.js/Vite tooling

## Dependencies

**Core**: React 19.x, React-DOM 19.x
**Dev**: TypeScript ~5.8.3, Vite 7.x, ESLint 9.x with TypeScript-ESLint